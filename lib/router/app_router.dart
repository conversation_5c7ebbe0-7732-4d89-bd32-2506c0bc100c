import 'package:dineazy_guest_kiosk/screens/cart/orders_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';

import '/common/constants/constants.dart';
import '/screens/cart/cart_details_screen.dart';
import '/screens/home/<USER>';
import '/screens/loading_screen.dart';

BuildContext? get globalNavigatorState => globalNavigatorKey.currentContext;

GoRouter getRouterConfig(String initialRoute) {
  return GoRouter(
    initialLocation: initialRoute,
    navigatorKey: globalNavigatorKey,
    routes: [
      GoRoute(path: '/', builder: (context, state) => const LoadingScreen()),
      GoRoute(path: '/home', builder: (context, state) => const HomeScreen()),
      GoRoute(path: '/cart', builder: (context, state) => const CartDetailsScreen()),
      GoRoute(path: '/orders', builder: (context, state) => const OrdersScreen()),
    ],
  );
}
