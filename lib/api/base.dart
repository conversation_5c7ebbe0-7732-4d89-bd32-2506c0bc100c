import 'package:flutter/cupertino.dart';
import 'package:http/http.dart';

import '../common/constants/constants.dart';


const _tokenKey = "access-token";

class ApiManagerBase {
  final Client client = Client();

  static void clearToken() => sharedPreferences.remove(_tokenKey);

  void logRequest(BaseRequest? request, Object body) {
    debugPrint("ApiManagerBase.logRequest:"
        "\n\t\turl: ${request?.url}"
        "\n\t\tmethod: ${request?.method}"
        "\n\t\theaders: ${request?.headers}"
        "\n\t\tbody: $body");
  }

  void logResponse(Response response) {
    debugPrint("ApiManagerBase.logResponse: "
        "\n\t\tstatusCode: ${response.statusCode}"
        "\n\t\tbody: ${response.body}");
  }

  void saveToken(String token) => sharedPreferences.setString(_tokenKey, token);

  String? getToken() => sharedPreferences.getString(_tokenKey);

  @mustCallSuper
  void dispose() {
    client.close();
  }
}
