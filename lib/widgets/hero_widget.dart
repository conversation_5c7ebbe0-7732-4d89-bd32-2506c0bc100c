import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../common/constants/colors.dart';

class HeroWidget extends StatelessWidget {
  final Animation<double> floatingAnimation;
  final Animation<double>? iconAnimation;
  final AnimationController floatingController;
  final Size size;
  final String title;

  const HeroWidget({
    super.key,
    required this.floatingAnimation,
    this.iconAnimation,
    required this.title,
    required this.floatingController,
    required this.size,
    rew
  });

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: "appHero",
      child: Stack(
        alignment: Alignment.center,
        children: [
          ...List.generate(6, (index) {
            return AnimatedBuilder(
              animation: floatingController,
              builder: (context, child) {
                final angle = (index * 60.0) + (floatingController.value * 30);

                final foodIcons = [
                  Icons.restaurant,
                  Icons.cake,
                  Icons.local_drink,
                  Icons.fastfood,
                  Icons.local_pizza,
                  Icons.local_cafe,
                ];
                final icon = foodIcons[index % foodIcons.length];

                return Positioned(
                  left: size.width * 0.1 + (index * size.width * 0.15),
                  top: size.height * 0.2 + (math.sin(angle * math.pi / 180) * 50),
                  child: Transform.rotate(
                    angle: angle * math.pi / 180,
                    child: Icon(icon, size: 40 + (index * 10), color: AppColors.lightGrey.withAlpha(100)),
                  ),
                );
              },
            );
          }),

          // Main content (icon + texts)
          Center(
            child: AnimatedBuilder(
              animation: iconAnimation?? floatingController,
              builder: (context, child) {
                final scale = iconAnimation?.value ?? 1.0;
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated app icon with floating effect
                    AnimatedBuilder(
                      animation: floatingAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, floatingAnimation.value),
                          child: Transform.scale(
                            scale: scale,
                            child: Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors.lightGrey.withAlpha(65),
                                border: Border.all(color: AppColors.lightGrey.withAlpha(30), width: 3),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.white.withAlpha(10),
                                    offset: const Offset(0, 20),
                                    blurRadius: 40,
                                  ),
                                ],
                              ),
                              child: const Icon(Icons.restaurant_menu, size: 80, color: AppColors.white),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 60),

                    const Text(
                      'DineEazy',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 48,
                        fontWeight: FontWeight.w900,
                        letterSpacing: -2,
                        height: 1.1,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Tagline
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      decoration: BoxDecoration(
                        color: AppColors.lightGrey.withAlpha(50),
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(color: AppColors.lightGrey.withAlpha(10)),
                      ),
                      child: Text(
                        title,
                        style: TextStyle(
                          color: AppColors.white.withAlpha(200),
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
