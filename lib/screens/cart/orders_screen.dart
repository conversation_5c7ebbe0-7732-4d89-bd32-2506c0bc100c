import 'package:dineazy_guest_kiosk/models/order/order_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';

import '../../common/constants/colors.dart';
import '../../models/food/food_sub_category.dart';
import '../../providers/cart_provider.dart';
import '../../providers/data_provider.dart';
import '../../widgets/add_on_popup.dart';
import '../../widgets/cart_bottom_bar.dart';
import '../../widgets/item_card.dart';
import '../../widgets/loading_screen.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  FoodSubCategory? selectedCategory;

  final List<String> categories = ["Main Course", "Sides", "Beverages", "Desserts"];

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(duration: const Duration(milliseconds: 500), vsync: this);
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));
    _fadeController.forward();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
      if (dataProvider.hasMenu) {
        return;
      }
      debugPrint("_OrdersScreenState.initState 🐞, HasMEnu: ${dataProvider.hasMenu}");
      dataProvider.loadMenu(dataProvider.restaurant, OrderType.takeaway);
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
      if (dataProvider.isMenuLoading && dataProvider.hasMenu) {
        return;
      }
      debugPrint("_OrdersScreenState.dispose 🐞, HasMEnu: ${dataProvider.hasMenu}");
      dataProvider.loadMenu(dataProvider.restaurant, OrderType.takeaway);
    });
  }


  @override
  Widget build(BuildContext context) {
    final cartProvider = Provider.of<CartProvider>(context);
    final DataProvider dataProvider = Provider.of<DataProvider>(context);

    if (dataProvider.isMenuLoading) {
      return Scaffold(body: LoadingScreen());
    }

    final menuItems = dataProvider.menu;

    final subCategories = dataProvider.menu.subCategories;

    selectedCategory = subCategories.first;

    debugPrint("OrdersScreen.build 🐞, SubCategories: ${subCategories.length}");

    final filteredMenuItems = selectedCategory == null
        ? menuItems.allFoodItems
        : menuItems.getItemsOfSubCategory(selectedCategory!.id);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.fromLTRB(32, 60, 32, 24),
              decoration: BoxDecoration(gradient: AppColors.cardGradient, boxShadow: AppColors.elevatedShadow),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.secondary.withAlpha(1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(Icons.restaurant_menu, color: AppColors.secondary, size: 28),
                      ),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Our Menu',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.w800,
                              color: AppColors.textPrimary,
                              letterSpacing: -1,
                            ),
                          ),
                          const Text(
                            'Touch to add items to your order',
                            style: TextStyle(fontSize: 16, color: AppColors.textSecondary, fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 14),

                  // Category Filter
                  SizedBox(
                    height: 50,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: subCategories.length,
                      itemBuilder: (context, index) {
                        final subCategory = subCategories.elementAt(index);
                        final isSelected = subCategory == selectedCategory;

                        return Padding(
                          padding: EdgeInsets.only(left: 24),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            child: FilterChip(
                              label: Text(
                                subCategory.name,
                                style: TextStyle(
                                  color: isSelected ? AppColors.textOnDark : AppColors.textSecondary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 18,
                                ),
                              ),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() => selectedCategory = subCategory);
                              },
                              backgroundColor: AppColors.filterUnselected,
                              selectedColor: AppColors.primary,
                              checkmarkColor: AppColors.textOnDark,
                              elevation: isSelected ? 4 : 1,
                              shadowColor: AppColors.filterSelected.withValues(alpha: 0.3),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                                side: BorderSide(color: isSelected ? AppColors.filterSelected : AppColors.filterBorder),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: Container(
                margin: const EdgeInsets.only(left: 30, right: 30, bottom: 20),
                child: filteredMenuItems.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.search_off, size: 64, color: AppColors.textTertiary),
                            const SizedBox(height: 16),
                            const Text(
                              'No items in this category',
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      )
                    : GridView.builder(
                        physics: const BouncingScrollPhysics(),
                        gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                          maxCrossAxisExtent: 400,
                          crossAxisSpacing: 24,
                          mainAxisSpacing: 24,
                          childAspectRatio: 0.78,
                        ),
                        itemCount: filteredMenuItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredMenuItems.elementAt(index);

                          List<AddOnItem>? addOnItems;

                          return AnimatedContainer(
                            duration: Duration(milliseconds: 200 + (index * 50)),
                            curve: Curves.easeOutBack,
                            child: ItemCard(
                              item: item,
                              // addOnItems: addOnItems,
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 350),
        child: CartBottomBar(isCartEmpty: cartProvider.isCartEmpty),
      ),
    );
  }
}
