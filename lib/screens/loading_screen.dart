import 'package:dineazy_guest_kiosk/widgets/hero_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../providers/data_provider.dart';
import '../common/constants/colors.dart';

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _iconController;
  late AnimationController _floatingController;
  late Animation<double> _iconAnimation;
  late Animation<double> _floatingAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _iconController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    // Create animations
    _iconAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    ));

    _floatingAnimation = Tween<double>(
      begin: -10,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    // Start icon animation
    _iconController.forward();

    // Handle data loading and navigation
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
      if (dataProvider.isRestaurantLoading && dataProvider.hasRestaurant) {
        return;
      }
      dataProvider.loadRestaurant();

      Future.delayed(const Duration(seconds: 3), () {
        context.go('/home');
      });
    });
  }

  @override
  void dispose() {
    _iconController.dispose();
    _floatingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.gradientStart,
              AppColors.gradientMiddle,
              AppColors.gradientEnd,
            ],
          ),
        ),
        child: Stack(
          children: [
            HeroWidget(
              title: 'Guest Kiosk • Self-Service Ordering',
              floatingAnimation: _floatingAnimation,
              iconAnimation: _iconAnimation,
              floatingController: _floatingController,
              size: size,
            ),

            // Bottom branding
            Positioned(
              bottom: 40,
              left: 0,
              right: 0,
              child: Text(
                'Powered by DineEazy',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.white.withAlpha(150),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}