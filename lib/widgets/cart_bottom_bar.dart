import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../common/constants/colors.dart';
import '../providers/cart_provider.dart';

class CartBottomBar extends StatelessWidget {
  final bool isCartEmpty;
  // final bool hasAddons;
  final Color? buttonColor;
  final TextStyle? textStyle;

  const CartBottomBar({required this.isCartEmpty, this.buttonColor, this.textStyle, super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CartProvider>(
      builder: (context, cartProvider, _) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: cartProvider.isCartEmpty ? 0 : 150,
          child: Container(
            margin: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [BoxShadow(color: AppColors.buttonPrimary.withValues(alpha: 0.3), offset: const Offset(0, 8), blurRadius: 20)],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(color: AppColors.overlayLight, borderRadius: BorderRadius.circular(12)),
                    child: const Icon(Icons.shopping_cart, color: AppColors.textOnDark, size: 40),
                  ),
                  const SizedBox(width: 10),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            '${cartProvider.totalItems} items in cart',
                            style: const TextStyle(color: Colors.white, fontSize: 28, fontWeight: FontWeight.w600),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            softWrap: false,
                          ),
                        ),
                        if (cartProvider.totalPrice > 0)
                          Flexible(
                            child: Text(
                              '\$${cartProvider.totalPrice.toStringAsFixed(2)}',
                              style: const TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.w400),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              softWrap: false,
                            ),
                          ),
                      ],
                    ),
                  ),

                  ElevatedButton(
                    onPressed: () {
                      // Handle checkout
                      context.go('/cart');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.white,
                      foregroundColor: AppColors.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                    ),
                    child: const Text('Checkout', style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
