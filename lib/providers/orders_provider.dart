import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';

import '../models/order/order.dart';
import '../models/order/order_status.dart';
import '../models/restaurant/restaurant.dart';
import 'data_provider.dart';

class OrdersProvider extends ChangeNotifier {
  OrdersProvider();

  bool _isLoading = false;

  bool loginRequired = false;

  bool get isLoading => _isLoading;

  final Map<String, Order> _orders = {};

  Iterable<Order> get orders => _orders.values;

  bool _isAdditionalChargesDomainValuesLoaded = false;

  OrdersIterable sortedOrders() {
    OrdersIterable ordersList = List.from(
      _orders.values.where((element) => element.hasValidItems),
    )..sort((a, b) {
        return -1 * a.updatedAt.compareTo(b.updatedAt);
      });
    return ordersList;
  }

  OrdersIterable getPendingOrders() => List.from(
        sortedOrders().where(
          (element) => element.hasPendingItems,
        ),
      );
  List<OrderStatus> onGoingStatuses = [
    OrderStatus.approved,
    OrderStatus.ready,
  ];
  OrdersIterable getOngoingOrders() {

    return List.from(
      sortedOrders().where((element) {
        return element.hasItemsOfStatus(onGoingStatuses);
      }),
    );
  }

  // All orders with status completed, void and complimentary
  OrdersIterable getCompletedOrders() => List.from(
        sortedOrders().where(
          (element) {
            return element.status == OrderStatus.completed ||  element.status == OrderStatus.voided ||
                element.status == OrderStatus.complimentary || element.status == OrderStatus.nonChargeable;

          },
        ),
      );

  // Get Declined, Not Delivered and Cancelled orders
  OrdersIterable getDeclinedOrders() => List.from(
    orders.where(
          (element) {
            return element.status == OrderStatus.declined ||  element.status == OrderStatus.cancelled ||
                element.status == OrderStatus.notDelivered;

          },
        ),
      );

  void updateOrder(Order order) {
    _orders[order.id] = order;
    notifyListeners();
  }

  Future<void> loadOrders(BuildContext context) async {
    _isLoading = true;
    notifyListeners();

    DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
    Restaurant restaurant = dataProvider.restaurant!;

    // if (!_isAdditionalChargesDomainValuesLoaded) {
    //   try {
    //     ApiManager apiManager = ApiManager();
    //     await Future.wait([
    //       apiManager.loadAdditionalChargesValues(restaurant.revenueCenterId),
    //       apiManager.loadAutoChargesValues(restaurant.revenueCenterId),
    //     ]);
    //     _isAdditionalChargesDomainValuesLoaded = true;
    //   } catch (_) {
    //     _isAdditionalChargesDomainValuesLoaded = false;
    //   }
    // }

    // try {
    //   ApiManager apiManager = ApiManager.tokenInstance();
    //   OrdersIterable newOrders;
    //
    //   if (isRunner || isSupervisor) {
    //     newOrders = await apiManager.getRunnerOrderHistory(restaurant, OrderType.dineIn.id);
    //   } else {
    //     newOrders = await apiManager.getGuestOrderHistory(
    //       restaurant,
    //       phone: profile.phone,
    //       email: profile.email,
    //     );
    //   }
    //
    //   final Set<String> incomingIds = {};
    //
    //   for (Order newOrder in newOrders) {
    //     if (isRunner && newOrder.status != OrderStatus.pending && newOrder.runnerId != profile.id) {
    //       continue;
    //     }
    //
    //     incomingIds.add(newOrder.id);
    //
    //     final existingOrder = _orders[newOrder.id];
    //
    //     if (existingOrder == null || existingOrder.updatedAt != newOrder.updatedAt) {
    //       _orders[newOrder.id] = newOrder;
    //     }
    //   }
    //
    //   // Optionally remove orders not in the latest fetch
    //   _orders.removeWhere((id, _) => !incomingIds.contains(id));
    //
    //   loginRequired = false;
    // } catch (e, st) {
    //   debugPrint("OrdersProvider.loadOrders: ❌ $e\n$st");
    //   clearOrders();
    //   loginRequired = true;
    // }

    _isLoading = false;
    notifyListeners();
  }


  void clearOrders() {
    _orders.clear();
    notifyListeners();
  }

  /// Updates order from backend
  ///
  /// Return order if update was successful and null otherwise
  // Future<Order?> syncOrder(Order order) async {
  //   try {
  //     ApiManager apiManager = ApiManager.tokenInstance();
  //     Order updatedOrder = await apiManager.syncOrder(order);
  //     updateOrder(updatedOrder);
  //     return updatedOrder;
  //   } catch (e) {
  //     debugPrint("OrdersView._updateOrder: ERROR: $e");
  //   }
  //   return null;
  // }

  Order? getOrderById(String id) => _orders[id];

  Map<String, int> getOrdersCountByUserId(String userId) {
    int pendingOrdersCount = 0;
    int completedOrdersCount = 0;
    int ongoingOrdersCount = 0;

    for (Order order in _orders.values) {
      if (order.runnerId == userId) {
        if (order.hasPendingItems) {
          pendingOrdersCount++;
        } else if (order.status == OrderStatus.completed) {
          completedOrdersCount++;
        } else if (order.hasItemsOfStatus(onGoingStatuses)) {
          ongoingOrdersCount++;
        }
      }
    }

    return {
      'pending': pendingOrdersCount,
      'completed': completedOrdersCount,
      'ongoing': ongoingOrdersCount,
    };
  }
}
