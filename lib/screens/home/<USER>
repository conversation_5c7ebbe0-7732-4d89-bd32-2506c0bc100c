import 'package:dineazy_guest_kiosk/screens/loading_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../common/constants/colors.dart';
import '../../providers/data_provider.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _pulseController;
  late Animation<double> _floatingAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _floatingController = AnimationController(duration: const Duration(seconds: 3), vsync: this)..repeat(reverse: true);

    _pulseController = AnimationController(duration: const Duration(seconds: 2), vsync: this)..repeat(reverse: true);

    _floatingAnimation = Tween<double>(
      begin: -10,
      end: 10,
    ).animate(CurvedAnimation(parent: _floatingController, curve: Curves.easeInOut));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final DataProvider dataProvider = Provider.of<DataProvider>(context);

    if (dataProvider.isRestaurantLoading) {
      return LoadingScreen();
    }

    final restaurant = dataProvider.restaurant;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [AppColors.gradientStart, AppColors.gradientMiddle, AppColors.gradientEnd],
          ),
        ),
        child: Stack(
          children: [
            ...List.generate(6, (index) {
              return AnimatedBuilder(
                animation: _floatingController,
                builder: (context, child) {
                  final angle = (index * 60.0) + (_floatingController.value * 30);

                  final foodIcons = [
                    Icons.restaurant,
                    Icons.cake,
                    Icons.local_drink,
                    Icons.fastfood,
                    Icons.local_pizza,
                    Icons.local_cafe,
                  ];
                  final icon = foodIcons[index % foodIcons.length];

                  return Positioned(
                    left: size.width * 0.1 + (index * size.width * 0.15),
                    top: size.height * 0.2 + (math.sin(angle * math.pi / 180) * 50),
                    child: Transform.rotate(
                      angle: angle * math.pi / 180,
                      child: Icon(icon, size: 40 + (index * 10), color: AppColors.lightGrey.withAlpha(100)),
                    ),
                  );
                },
              );
            }),

            // Main Content
            SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      // Header Section
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppColors.lightGrey.withAlpha(60),
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(color: AppColors.white.withAlpha(20)),
                            ),
                            child: const Icon(Icons.restaurant, color: Colors.white, size: 32),
                          ),
                          const SizedBox(width: 20),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                restaurant.name,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 28,
                                  fontWeight: FontWeight.w800,
                                  letterSpacing: -0.5,
                                ),
                              ),
                              Text(
                                'Self-Service Ordering',
                                style: TextStyle(
                                  color: AppColors.white.withAlpha(200),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: AppColors.lightGrey.withAlpha(60),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.access_time, color: AppColors.white, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'Open 24/7',
                                  style: TextStyle(color: AppColors.white, fontSize: 14, fontWeight: FontWeight.w600),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 80),

                      // Hero Section
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AnimatedBuilder(
                            animation: _floatingAnimation,
                            builder: (context, child) {
                              return Transform.translate(
                                offset: Offset(0, _floatingAnimation.value),
                                child: Container(
                                  width: 200,
                                  height: 200,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppColors.lightGrey.withAlpha(65),
                                    border: Border.all(color: AppColors.lightGrey.withAlpha(30), width: 3),
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.white.withAlpha(10),
                                        offset: const Offset(0, 20),
                                        blurRadius: 40,
                                      ),
                                    ],
                                  ),
                                  child: const Icon(Icons.touch_app, size: 80, color: AppColors.white),
                                ),
                              );
                            },
                          ),

                          const SizedBox(height: 10),

                          const Text(
                            'Welcome to',
                            style: TextStyle(color: AppColors.white, fontSize: 24, fontWeight: FontWeight.w400),
                          ),

                          const SizedBox(height: 8),

                          const Text(
                            'Digital Dining',
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 48,
                              fontWeight: FontWeight.w900,
                              letterSpacing: -2,
                              height: 1.1,
                            ),
                          ),

                          const SizedBox(height: 16),

                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            decoration: BoxDecoration(
                              color: AppColors.lightGrey.withAlpha(50),
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(color: AppColors.lightGrey.withAlpha(10)),
                            ),
                            child: Text(
                              'Order fresh food in seconds • No waiting in line',
                              style: TextStyle(
                                color: AppColors.white.withAlpha(200),
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),

                          const SizedBox(height: 20),

                          // Action Buttons
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Browse Menu Button
                              AnimatedBuilder(
                                animation: _pulseAnimation,
                                builder: (context, child) {
                                  return Transform.scale(
                                    scale: _pulseAnimation.value,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.lightGrey.withAlpha(5),
                                            offset: const Offset(0, 8),
                                            blurRadius: 20,
                                            spreadRadius: 0,
                                          ),
                                        ], // 0.3 opacity
                                      ),
                                      child: ElevatedButton(
                                        onPressed: () {
                                          context.go('/orders');
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.white,
                                          foregroundColor: AppColors.filterSelected,
                                          elevation: 0,
                                          padding: const EdgeInsets.symmetric(horizontal: 48, vertical: 24),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                                        ),
                                        child: const Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.restaurant_menu, size: 28),
                                            SizedBox(width: 12),
                                            Text(
                                              'Browse Menu',
                                              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w700),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(width: 12),

                              // Quick Order Button
                              // Container(
                              //   decoration: BoxDecoration(
                              //     borderRadius: BorderRadius.circular(20),
                              //     border: Border.all(color: const Color(0x66FFFFFF), width: 2), // 0.4 opacity (0.4 * 255 = 102)
                              //   ),
                              //   child: ElevatedButton(
                              //     onPressed: () {
                              //       // Quick order functionality
                              //     },
                              //     style: ElevatedButton.styleFrom(
                              //       backgroundColor: Colors.transparent,
                              //       foregroundColor: Colors.white,
                              //       elevation: 0,
                              //       padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
                              //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
                              //     ),
                              //     child: const Row(
                              //       mainAxisSize: MainAxisSize.min,
                              //       children: [
                              //         Icon(Icons.flash_on, size: 24),
                              //         SizedBox(width: 8),
                              //         Text('Quick Order', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                              //       ],
                              //     ),
                              //   ),
                              // ),
                            ],
                          ),
                        ],
                      ),

                      // Features Section
                      Container(
                        padding: const EdgeInsets.all(32),
                        margin: const EdgeInsets.only(top: 40),
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey.withAlpha(20),
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(color: AppColors.lightGrey.withAlpha(10)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildFeature(Icons.speed, 'Fast Service', 'Order in under 2 minutes'),
                            Container(width: 1, height: 30, color: AppColors.lightGrey),
                            _buildFeature(Icons.payment, 'Easy Payment', 'Card, cash, or mobile pay'),
                            Container(width: 1, height: 30, color: AppColors.lightGrey), // 0.3 opacity
                            _buildFeature(Icons.eco, 'Fresh & Local', 'Sourced daily from local farms'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeature(IconData icon, String title, String description) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(16)),
          child: Icon(icon, color: AppColors.textTertiary, size: 46),
        ),
        const SizedBox(height: 6),
        Text(
          title,
          style: const TextStyle(color: AppColors.white, fontSize: 25, fontWeight: FontWeight.w700),
        ),
        const SizedBox(height: 2),
        Text(
          description,
          style: TextStyle(color: AppColors.white, fontSize: 18, fontWeight: FontWeight.w500), // 0.8 opacity
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
