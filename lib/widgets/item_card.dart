import 'dart:async';
import 'package:dineazy_guest_kiosk/models/food/food_item.dart';
// import 'package:dineazy_guest_kiosk/models/add_on_item.dart'; // Unused import
import 'package:dineazy_guest_kiosk/providers/data_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../common/constants/colors.dart';
import '../providers/cart_provider.dart';
import '../widgets/add_on_popup.dart'; // Assuming showAddOnPopup is defined here

class ItemCard extends StatefulWidget {
  final FoodItem item;

  const ItemCard({required this.item, super.key});

  @override
  State<ItemCard> createState() => _ItemCardState();
}

class _ItemCardState extends State<ItemCard> with TickerProviderStateMixin {
  String state = "add";
  bool _isProcessing = false;
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(duration: const Duration(milliseconds: 600), vsync: this);
    _scaleController = AnimationController(duration: const Duration(milliseconds: 200), vsync: this);

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut));
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.97).animate(CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onAddPressed(CartProvider cartProvider) async {
    if (state != "add" || _isProcessing) return;

    _isProcessing = true;

    await _scaleController.forward();
    _scaleController.reverse();

    if (widget.item.hasAddons) {
      _showAddOnPopup(cartProvider);
      _isProcessing = false; 
    } else {
      // CRITICAL: Adjust this call to match your CartProvider.addCartItem method signature.
      // Assuming a signature like: addCartItem(String itemId, double itemPrice, String itemName, int quantity)
      // Or if it simply takes (String name, double price), then use:
      // cartProvider.addCartItem(widget.item.name, widget.item.price);
      // For now, using a common pattern:
      cartProvider.addCartItem(widget.item.id, widget.item.price, widget.item.name, 1); 
      if (mounted) {
        setState(() => state = "tick");
        await _pulseController.forward();
        _pulseController.reverse();

        Future.delayed(const Duration(milliseconds: 700), () {
          if (mounted) {
            setState(() => state = "quantity");
            _isProcessing = false;
          }
        });
      }
    }
  }

  void _showAddOnPopup(CartProvider cartProvider) {
    // Assuming showAddOnPopup is a global function:
    // showAddOnPopup(BuildContext context, FoodItem foodItem, CartProvider cartProvider, Function(String) onStateChange)
    showAddOnPopup( 
        context,
        widget.item,
        cartProvider,
        (newState) { // onStateChange callback
            if (mounted) {
              final quantity = cartProvider.getQuantity(widget.item.id);
              if (quantity > 0 && state != "quantity") {
                setState(() => state = "quantity");
              } else if (quantity == 0 && state != "add") {
                // Potentially reset to "add" if item was removed via popup
                // setState(() => state = "add");
              }
              if (newState == "itemAdded" || newState == "itemAddedViaSkip") { // Handle states from popup
                 setState(() => state = "tick");
                 _pulseController.forward().then((_) => _pulseController.reverse());
                 Future.delayed(const Duration(milliseconds: 700), () {
                   if (mounted) {
                     setState(() => state = "quantity");
                   }
                 });
              }
            }
        },
    );
  }

  @override
  Widget build(BuildContext context) {
    final cartProvider = Provider.of<CartProvider>(context);
    final quantity = cartProvider.getQuantity(widget.item.id);

    final DataProvider dataProvider = Provider.of<DataProvider>(context);
    final menu = dataProvider.menu;

    if (quantity == 0 && state != "add" && state != "tick") { // Avoid resetting state during "tick"
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && cartProvider.getQuantity(widget.item.id) == 0 && state != "add") {
          setState(() => state = "add");
        }
      });
    }

    String imagePath = "";
    if (widget.item.imageId != null) {
      imagePath = menu.getImageUrlById(widget.item.imageId!);
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return AnimatedBuilder(
          animation: Listenable.merge([_pulseController, _scaleController]),
          builder: (context, child) {
            final scaleValue = _scaleAnimation.value;
            final pulseValue = _pulseAnimation.value;

            return Transform.scale(
              scale: scaleValue * pulseValue,
              child: Container(
                width: 260,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [BoxShadow(color: Colors.black.withAlpha(7), offset: Offset(2, 8), blurRadius: 24)],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(30), topRight: Radius.circular(30)),
                      child: imagePath.isNotEmpty
                          ? Image.asset(
                              imagePath,
                              height: 170,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => Container(
                                height: 170,
                                color: Colors.grey[200],
                                child: Icon(Icons.broken_image, size: 50, color: Colors.grey),
                              ),
                            )
                          : Container(
                              height: 170,
                              color: Colors.grey[200],
                              child: Icon(Icons.image_not_supported, size: 50, color: Colors.grey),
                            ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.item.name,
                            style: TextStyle(fontSize: 22, fontWeight: FontWeight.w700, color: AppColors.textPrimary),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            widget.item.description ?? \'\',
                            style: TextStyle(fontSize: 15, color: AppColors.textSecondary),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 10),
                          Text(
                            \'\$${widget.item.price.toStringAsFixed(2)}\',
                            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppColors.primary),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    Padding(padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16), child: _buildActionWidget(cartProvider, quantity)),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildActionWidget(CartProvider cartProvider, int quantity) {
    if (state != "tick") {
      if (quantity > 0) {
        state = "quantity";
      } else {
        state = "add";
      }
    }

    if (state == "add") {
      return ElevatedButton(
        onPressed: () => _onAddPressed(cartProvider),
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(AppColors.primary),
          foregroundColor: WidgetStateProperty.all(Colors.white),
          elevation: WidgetStateProperty.all(3),
          shape: WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(16))),
          padding: WidgetStateProperty.all(EdgeInsets.symmetric(vertical: 18)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_shopping_cart, size: 28),
            SizedBox(width: 7),
            Text(\'Add\', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24)),
          ],
        ),
      );
    } else if (state == "tick") {
      return Container(
        width: double.infinity,
        height: 65,
        padding: EdgeInsets.symmetric(vertical: 18),
        decoration: BoxDecoration(color: AppColors.success, borderRadius: BorderRadius.circular(16)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 28),
            SizedBox(width: 8),
            Text(
              \'Added\',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 24),
            ),
          ],
        ),
      );
    } else { // "quantity" state
      return Container(
        height: 65, 
        decoration: BoxDecoration(
          color: AppColors.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.primary.withAlpha(16)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              icon: Icon(quantity == 1 ? Icons.delete_outline : Icons.remove, size: 32),
              color: quantity == 1 ? Colors.redAccent : Colors.black87,
              onPressed: () => cartProvider.removeCartItem(widget.item.id),
            ),
            Text(
              \'$quantity\',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24, color: AppColors.black),
            ),
            IconButton(
              icon: Icon(Icons.add, size: 32), 
              color: AppColors.primary, 
              // CRITICAL: Adjust this call to match your CartProvider.addCartItem method signature.
              // Assuming a signature like: addCartItem(String itemId, double itemPrice, String itemName, int quantity)
              // Or if it simply takes (String name, double price), then use:
              // cartProvider.addCartItem(widget.item.name, widget.item.price);
              // For now, using a common pattern:
              onPressed: () => cartProvider.addCartItem(widget.item.id, widget.item.price, widget.item.name, 1)
            ),
          ],
        ),
      );
    }
  }
}
