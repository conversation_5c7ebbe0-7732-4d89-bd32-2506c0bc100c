import 'package:flutter/material.dart';

import '../common/types.dart';
import '../models/add_on_item.dart';
import '../models/base.dart';
import '../models/food/food_item.dart';
import '../models/order/order_item.dart';
import '../models/order/order_status.dart';
import '../models/order/order_type.dart';
import 'data_provider.dart';

class CartProvider extends ChangeNotifier {
  static final CartProvider _mInstance = CartProvider._internal();

  factory CartProvider() => _mInstance;

  CartProvider._internal();

  final DataProvider _dataProvider = DataProvider();

  final List<CartItem> _items = [];

  Iterable<CartItem> get items => _items;

  int get totalItemsCount => _items.isEmpty ? 0 : items.map((e) => e.count).reduce((value, element) => value + element);

  bool get isEmpty => _items.isEmpty;
  String instructions = "";


  double get grandTotal {
    double price = total;
    if (AutoChargesValues.values.isNotEmpty) {
      price += AutoChargesValues.values.map((e) => e.getCharge(total)).reduce((value, element) => value += element);
    }

    return price + tax;
  }

  double get total => items.map((e) => e.price).reduce((value, element) => value += element);

  double get tax => items.map((element) => element.tax).reduce((value, element) => value += element);

  OrderType? _orderType;

  OrderType? get orderType => _orderType;

  bool get isDineIn => _orderType?.isDineIn ?? false;

  void setOrderType(OrderType orderType) => _orderType = orderType;

  Iterable<CartItem> getItemsOfId(String id) => _items.where((element) => element.item.id == id);

  void setItems(Iterable<CartItem> items) {
    _items
      ..clear()
      ..addAll(items);
    notifyListeners();
  }

  int getItemCountById(String id) {
    int count = 0;
    for (CartItem item in _items.where((element) => element.id == id)) {
      count += item.count;
    }
    return count;
  }

  void addItemOfId(String id) {
    int existingItemIndex = _items.indexWhere((element) => element.id == id);

    FoodItem foodItem = _dataProvider.menu.getFoodItemById(id);

    if (existingItemIndex != -1) {
      int? limit = foodItem.limitPerOrder;
      if (limit != null && _items[existingItemIndex].count >= limit) {
        throw "You have exceeded the limit per order";
      }
      _items[existingItemIndex].count += 1;
    } else {
      _items.add(CartItem(foodItem));
    }
    notifyListeners();
  }

  void addItemByKey(Key key) {
    _items.firstWhere((element) => element.key == key).count += 1;
    notifyListeners();
  }

  void addAllItems(Iterable<CartItem> items) {
    List<CartItem> filtered = [];
    for (CartItem item in items) {
      int index = filtered.indexWhere((CartItem element) {
        return element == item;
      });

      if (index != -1) {
        //  duplicate item
        filtered[index].count += item.count;
      } else {
        filtered.add(item);
      }
    }
    _items.addAll(filtered);
    notifyListeners();
  }

  void removeAllItemsOfId(String id) {
    _items.removeWhere((element) => element.item.id == id);
    notifyListeners();
  }

  void removeItemByKey(Key key) {
    int existingItemIndex = _items.indexWhere((element) => element.key == key);

    if (existingItemIndex == -1) return;

    CartItem item = _items[existingItemIndex];
    item.count -= 1;
    if (item.count < 1) {
      _items.removeAt(existingItemIndex);
    }

    notifyListeners();
  }

  void removeItemById(String id) {
    int existingItemIndex = _items.indexWhere((element) => element.id == id);

    if (existingItemIndex == -1) return;

    CartItem item = _items[existingItemIndex];
    item.count -= 1;
    if (item.count < 1) {
      _items.removeAt(existingItemIndex);
    }

    notifyListeners();
  }

  //   /// Generates JSON request object for "Place Order API"
  //   JSONObject _createOrderRequest() {
  //     if (items.isEmpty) {
  //       throw "No Items in Cart";
  //     }
  //
  //     OrderType? orderType = _orderType;
  //     if (orderType == null) {
  //       throw "Order type was not defined";
  //     }
  //
  //
  //     String sourceId;
  //
  //     if (DataProvider.profile.isEmployee) {
  //       status = OrderStatus.approved;
  //       sourceId = DomainValues.runnerSourceId;
  //     } else {
  //       status = OrderStatus.pending;
  //       sourceId = DomainValues.guestSourceId;
  //     }
  //
  //     var currency = CurrencyValues.byId(_dataProvider.restaurant!.restaurantSettings.currencyValue).name;
  //
  //     final request = {
  //       "items": items
  //           .map(
  //             (e) => e.getItemMap(
  //           status: status,
  //           instructions: instructions,
  //         ),
  //       )
  //           .toList(),
  //       "sourceId": sourceId,
  //       "tableId": table?.id,
  //       "orderTypeId": orderType.id,
  //       "statusId": status.id,
  //       "currency": currency,
  //     };
  //
  //     if (_customer != null) {
  //       request["customer"] = {
  //         "firstName": _customer!.firstName,
  //         "lastName": _customer!.lastName,
  //         "email": _customer!.email,
  //         "phone": _customer!.phone,
  //       };
  //     }
  //
  //     if (table?.reservationId != null) {
  //       request["reservationId"] = table?.reservationId;
  //     }
  //
  //     return request;
  //   }
  //
  //   Future<Order?> _dispatchDineInOrder(BuildContext context, JSONObject orderRequest) async {
  //     LoadingDialog loadingDialog = LoadingDialog(context, "Placing Order...");
  //     loadingDialog.show();
  //
  //     OrdersProvider ordersProvider = Provider.of<OrdersProvider>(context, listen: false);
  //
  //     try {
  //       ApiManager apiManager = ApiManager.tokenInstance();
  //       Restaurant restaurant = _dataProvider.restaurant!;
  //
  //       String? orderId = table?.orderId;
  //       Order order = orderId != null
  //           ? await apiManager.addKotToOrder(restaurant, orderId, orderRequest)
  //           : await apiManager.placeOrder(restaurant, orderRequest);
  //
  //       loadingDialog.dismiss();
  //
  //       debugPrint("CartProvider._dispatchDineInOrder: order created successfully: ${order.id}");
  //       ordersProvider.updateOrder(order);
  //
  //       return order;
  //     } catch (e, st) {
  //       debugPrint("CartProvider._dispatchDineInOrder: ❌ERROR: $e\n$st");
  //       loadingDialog.dismiss();
  //       rethrow;
  //     }
  //   }
  //
  //   /// __Take-Away__ order flow
  //   Future<Order?> _dispatchTakeawayOrder(BuildContext context, Map<String, dynamic> orderRequest) async {
  //     debugPrint("CartProvider._dispatchTakeawayOrder: START TAKEAWAY ORDER");
  //
  //     LoadingDialog loadingDialog = LoadingDialog(context, "Placing Order...");
  //     loadingDialog.show();
  //
  //     try {
  //       ApiManager apiManager = ApiManager.tokenInstance();
  //       Restaurant restaurant = _dataProvider.restaurant!;
  //
  //       Order orderResponse = await apiManager.placeOrder(restaurant, orderRequest);
  //
  //       await loadingDialog.dismiss();
  //
  //       _order = orderResponse;
  //       debugPrint("CartProvider._dispatchTakeawayOrder: order created successfully: ${order.id}");
  //       if (context.mounted) {
  //         await _showTipModal(context);
  //       }
  //
  //       // if awaited till end, on payment complete, _order is set to null
  //       // this will show order failed message even if order was successful
  //       if (context.mounted) {
  //         _proceedTakeawayPayment(context);
  //       }
  //       return _order;
  //     } catch (e, st) {
  //       debugPrint("CartProvider._dispatchTakeawayOrder: ❌ERROR: $e\n$st");
  //       if (context.mounted) {
  //         showErrorSnackBar(context, e.toString());
  //       }
  //       loadingDialog.dismiss();
  //       rethrow;
  //     }
  //   }
  //
  //   //  TODO: Improve Tip check flow for Takeaway
  //   Future<void> _showTipModal(BuildContext context) async {
  //     bool addTipResponse = await AddTipBottomSheet.show(
  //       context: context,
  //       order: order,
  //       onTipAdded: (updatedOrder) => _order = updatedOrder,
  //     );
  //
  //     if (!addTipResponse && context.mounted) return _showTipModal(context);
  //   }
  //
  //   Future<void> _onTakeawayPaymentSuccess(Restaurant restaurant, Order order) async {
  //     debugPrint("CartProvider._proceedTakeawayPayment: payment success");
  //     //  wait for loading dialog in payment util to dispose
  //
  //     BuildContext? context = globalNavigatorContext;
  //     if (context == null) return;
  //     OrdersProvider ordersProvider = Provider.of<OrdersProvider>(context, listen: false);
  //
  //     String companyId = order.companyId;
  //     String rvcId = order.revenueCenterId;
  //     String orderId = order.id;
  //     clear();
  //
  //     // TODO: Implement shareBillView
  //     // CartScreen.navigatorKey.currentState?.pushNamed(
  //     //   CartScreen.shareBillView,
  //     //   arguments: {
  //     //     Keys.companyId: companyId,
  //     //     Keys.revenueCenterId: rvcId,
  //     //     Keys.orderId: orderId,
  //     //   },
  //     // );
  //
  //     try {
  //       ApiManager apiManager = ApiManager.tokenInstance();
  //       Order updatedOrder = await apiManager.approveAllKotItems(restaurant, order);
  //       ordersProvider.updateOrder(updatedOrder);
  //       debugPrint("CartProvider._onTakeawayPaymentSuccess: order updated");
  //     } catch (e, st) {
  //       debugPrint("CartProvider._proceedTakeawayPayment: ERROR: not supposed to happen\nERROR: $e, $st");
  //     }
  //   }
  //
  //   /// Start Take-Away payment
  //   Future<void> _proceedTakeawayPayment(BuildContext context) async {
  //     PaymentGatewayService pgService = PaymentGatewayService(context);
  //
  //     if (_order == null) {
  //       debugPrint("CartProvider._proceedTakeawayPayment: ❌ERROR: Order not found");
  //       return;
  //     }
  //
  //     if (_order != null) {
  //       Restaurant restaurant = _dataProvider.restaurant!;
  //
  //       OnPaymentResponse onPaymentResponse = OnPaymentResponse(
  //         success: () async {
  //           pgService.dispose();
  //           _onTakeawayPaymentSuccess(restaurant, order);
  //         },
  //         failed: (String error) async {
  //           pgService.dispose();
  //           debugPrint("CartProvider._proceedTakeawayPayment: Payment Failed ($error)");
  //           showErrorSnackBar(context, error);
  //           await _cancelOrder(context, error);
  //         },
  //       );
  //
  //       await pgService.startPayment(_order!, restaurant, onPaymentResponse);
  //     } else {
  //       debugPrint("CartProvider._proceedTakeawayPayment: ERROR: no order found");
  //     }
  //   }
  //
  //   Future<void> _cancelOrder(BuildContext context, String reason) async {
  //     if (_order == null) {
  //       debugPrint("CartProvider.cancelOrder: no order to cancel");
  //       return;
  //     }
  //     debugPrint("CartProvider.cancelOrder: cancelling order..!");
  //
  //     Order order = _order!;
  //     OrdersProvider ordersProvider = Provider.of(context, listen: false);
  //
  //     try {
  //       ApiManager apiManager = ApiManager.tokenInstance();
  //       await apiManager.cancelOrder(order, reason);
  //       Order? existingOrder = ordersProvider.getOrderById(order.id);
  //       if (existingOrder != null) {
  //         existingOrder.modifyStatus(OrderStatus.cancelled);
  //         ordersProvider.updateOrder(existingOrder);
  //       }
  //
  //       _order = null;
  //     } catch (e) {
  //       debugPrint("CartProvider.cancelOrder: ERROR: $e");
  //     }
  //   }
  //
  //   /// Place Order and handle payment
  //   Future<void> dispatchOrder(
  //       BuildContext context, {
  //         required Function(BuildContext, Order) onSuccess,
  //         required Function(BuildContext, dynamic) onFailed,
  //       }) async {
  //     try {
  //       JSONObject orderRequest = _createOrderRequest();
  //       Order? order;
  //       if (isDineIn) {
  //         order = await _dispatchDineInOrder(context, orderRequest);
  //       } else {
  //         order = await _dispatchTakeawayOrder(context, orderRequest);
  //       }
  //
  //       if (order == null) {
  //         throw "Failed to create order";
  //       }
  //
  //       if (isDineIn) clear();
  //       if (context.mounted) {
  //         onSuccess.call(context, order);
  //       }
  //     } catch (e) {
  //       debugPrint("CartProvider.dispatchOrder: ❌ERROR: $e");
  //       if (context.mounted) {
  //         onFailed.call(context, e);
  //       }
  //     }
  //   }
}

class CartItem {
  String get id => item.id;

  /// This will be unique for each item
  final Key key = UniqueKey();

  final FoodItem item;

  CartItem(this.item) : super() {
    if (!item.hasAddons) return;

    for (AddOn addOn in item.addons) {
      for (AddOnDelegate addOnDelegate in addOn.items) {
        if (addOnDelegate.defaultSelected || addOnDelegate.mustSelected) {
          if (addOn.multiSelect) {
            multipleAddons.add(addOnDelegate);
          } else {
            singleAddons.add(addOnDelegate);
          }
        }
      }
    }
  }

  /// Private constructor to set from [OrderItem]
  CartItem._order(OrderItem orderItem) : item = orderItem.item, super() {
    for (AddOnDelegate addOnDelegate in orderItem.addonDelegates) {
      multipleAddons.add(addOnDelegate);
    }
  }

  factory CartItem.fromOrderItem(OrderItem orderItem) => CartItem._order(orderItem)
    ..count = orderItem.quantity
    ..orderItemId = orderItem.id
    ..kotNo = orderItem.kotNo;

  int count = 1;

  /// Id of [OrderItem] if this cart item was derived from OrderItem
  String? orderItemId;

  int? kotNo;

  double get tax => price * item.taxPercent;

  double get price {
    if (count == 0) return 0;

    double price = item.price;
    if (hasAddons) {
      for (AddOnDelegate addon in addons) {
        price += addon.price;
      }
    }

    return price * count;
  }

  List<AddOnDelegate> singleAddons = [];
  List<AddOnDelegate> multipleAddons = [];

  List<AddOnDelegate> get addons => [...singleAddons, ...multipleAddons];

  bool get hasAddons => addons.isNotEmpty;

  bool get hasSingleAddons => singleAddons.isNotEmpty;

  bool get hasMultipleAddons => multipleAddons.isNotEmpty;

  String get addonsFormattedText => addons.map((e) => e.item.name).join(", ");

  String? choiceId;

  List<String> customizationIds = [];

  bool get hasChoice => choiceId != null;

  bool get hasCustomizations => customizationIds.isNotEmpty;

  JSONObject getItemMap({OrderStatus status = OrderStatus.pending, String? instructions}) {
    JSONObject map = {
      "inventoryId": id,
      "inventoryName": item.name,
      "addons": addons.map((e) => e.requestMap).toList(),
      "quantity": count,
      "price": item.price,
      "statusId": status.id,
      "totalPrice": price,
    };

    if (kotNo != null) map["kotNumber"] = kotNo;
    if (instructions != null) map["kotInstructions"] = instructions;

    return map;
  }

  @override
  String toString() => "${item.id},$addonsFormattedText";

  @override
  int get hashCode => toString().hashCode;

  @override
  bool operator ==(Object other) {
    if (other is CartItem) {
      return hashCode == other.hashCode;
    }
    return false;
  }
}
