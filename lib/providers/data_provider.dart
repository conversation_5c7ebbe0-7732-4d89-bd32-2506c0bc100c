import 'package:flutter/cupertino.dart';

import '../api/api_manager.dart';
import '../common/env.dart';
import '../models/food/food_sub_category.dart';
import '../models/order/order_type.dart';
import '../models/restaurant/restaurant.dart';
import '../models/restaurant/restaurant_menu.dart';

class DataProvider extends ChangeNotifier {
  RestaurantMenu? _menu;

  bool _isLoadingMenu = false;

  bool get isMenuLoading => _isLoadingMenu;

  bool get hasMenu => _menu != null;

  bool _isRestaurantLoading = false;

  bool get isRestaurantLoading => _isRestaurantLoading;

  bool _hasRestaurant = false;

  bool get hasRestaurant => _hasRestaurant;

  RestaurantMenu get menu => _menu!;

  Restaurant? _restaurant;

  Restaurant get restaurant => _restaurant!;

  Future<void> loadRestaurant() async {
    _isRestaurantLoading = true;
    notifyListeners();
    ApiManager apiManager = ApiManager();
    await apiManager.loadDomainValues();

    _restaurant = await apiManager.getRestaurantDetails(Environment.companyId, Environment.restaurantId);
    _hasRestaurant = true;
    _isRestaurantLoading = false;
    notifyListeners();
  }

  Iterable<FoodSubCategory> getSubCategoriesOfId(String id) =>
      menu.subCategories.where((element) => element.parentId == id);

  Future<void> loadMenu(Restaurant restaurant, OrderType orderType) async {
    _isLoadingMenu = true;
    notifyListeners();

    debugPrint("DataProvider.loadMenu 🐞, restaurant ${restaurant.restaurantSettings.timezoneValue}");

    try {
      ApiManager apiManager = ApiManager();
      _menu = await apiManager.getMenu(
        companyId: restaurant.companyId,
        revenueCenterId: restaurant.revenueCenterId,
        orderTypeId: orderType.id,
        timezoneId: restaurant.restaurantSettings.timezoneValue,
      );
    } catch (e) {
      debugPrint("DataProvider.loadMenu: ❌ERROR: $e");
    }

    _isLoadingMenu = false;
    notifyListeners();
  }
}
