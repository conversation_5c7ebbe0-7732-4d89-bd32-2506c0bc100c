

import '../../common/types.dart';
import '../../providers/data_provider.dart';
import '../add_on_item.dart';
import '../food/food_item.dart';

typedef OrderAddOnList = List<OrderAddOn>;
typedef OrderAddOnIterable = Iterable<OrderAddOn>;

class OrderAddOn {
  final JSONObject json;

  OrderAddOn._(this.json);

  bool get defaultSelected => json["defaultSelected"];

  bool get disabled => json["disabled"];

  String get itemName => json["itemName"];

  bool get mustSelected => json["mustSelected"];

  bool get isInventory => json["isInventory"] ?? false;

  double get price => double.parse(json["price"].toString());

  int get quantity => json["quantity"];

  String get refId => json["refId"];

  bool get selected => json["selected"];

  AddOnItem get item {
    try {
      return DataProvider().menu.getAddOnById(refId);
    } catch (e) {
      //  ignore error
      FoodItem foodItem = DataProvider().menu.getFoodItemById(refId);
      return AddOnItem(foodItem.json);
    }
  }
}
