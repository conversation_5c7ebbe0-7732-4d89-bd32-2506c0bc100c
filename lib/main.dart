import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'common/theme/app_theme.dart';
import 'providers/cart_provider.dart';
import 'providers/data_provider.dart';
import 'providers/orders_provider.dart';
import 'router//app_router.dart';

void main() {
  String initialRoute = '/';
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CartProvider()),
        ChangeNotifierProvider(create: (_) => DataProvider()),
        ChangeNotifierProvider(create: (_) => OrdersProvider()),
      ],
      child: MyApp(initialRoute: initialRoute),
    ),
  );
}

class MyApp extends StatefulWidget {
  final String initialRoute;

  const MyApp({super.key, required this.initialRoute});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    final router = getRouterConfig(widget.initialRoute);
    return MaterialApp.router(
      title: 'Kiosk',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.theme,
      routerConfig: router,
    );
  }
}
