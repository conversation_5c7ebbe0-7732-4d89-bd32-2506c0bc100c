
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../common/types.dart';
import '../../providers/data_provider.dart';
import '../base.dart';
import 'order_additional_charge.dart';
import 'order_item.dart';
import 'order_status.dart';
import 'order_type.dart';
import '../payment_status.dart';
import '../restaurant/restaurant.dart';


typedef OrdersList = List<Order>;
typedef OrdersIterable = Iterable<Order>;

class Order {
  final JSONObject json;

  Order(this.json) {
    String now = DateTime.now().toString();
    if (!json.containsKey("createdAt")) {
      json["createdAt"] = now;
    }
    if (!json.containsKey("updatedAt")) {
      json["updatedAt"] = now;
    }
  }

  String get no => json["orderNumber"] is String ? json["orderNumber"] : json["orderNumber"].toString();

  String get id => json["_id"] ?? json["orderId"];

  double get price => double.parse(json["price"].toString());

  double get tax => double.parse(json["tax"].toString());

  bool get hasAdditionalCharges => additionalCharges.isNotEmpty;

  //  All Additional Charges (Contains Auto Charges)
  double get allAdditionalCharges {
    double otherCharges = 0.0;
    for (OrderAdditionalCharge item in additionalCharges) {
      otherCharges += item.amount;
    }

    return otherCharges;
  }

  //  Tip Charges
  double get tipCharge {
    double additionalCharge = 0.0;
    //  TODO: Add Tip check
    for (OrderAdditionalCharge item in additionalCharges) {
      debugPrint("Order.tipCharge: 🐞${item.json}");
    }
    return additionalCharge;
  }

  double get grandTotal => price + allAdditionalCharges + tax;

  DateTime get createdAt {
    DateTime dateTime = DateTime.parse(json["createdAt"]);
    Restaurant? restaurant = DataProvider().restaurant;
    if (restaurant == null) {
      return dateTime;
    }

    String timezoneId = restaurant.restaurantSettings.timezoneValue;
    String differenceString = TimezoneValues.byId(timezoneId);
    int difference = (double.parse(differenceString) * 60).toInt();
    return dateTime.add(Duration(minutes: difference));
  }

  String get _dateTimeFormat {
    RestaurantSettings? settings = DataProvider().restaurant?.restaurantSettings;
    if (settings == null) {
      return "dd MMM hh:mm a";
    }
    String dateFormatId = settings.dateFormatValue;
    String timeFormatId = settings.timeFormatValue;

    String dateFormat = DateFormatValues.byId(dateFormatId);
    String timeFormat = TimeFormatValues.byId(timeFormatId);
    return "$dateFormat $timeFormat";
  }

  String get formattedCreatedAt => DateFormat(_dateTimeFormat).format(createdAt);

  DateTime get updatedAt {
    DateTime dateTime = DateTime.parse(json["updatedAt"]);
    Restaurant? restaurant = DataProvider().restaurant;
    if (restaurant == null) {
      return dateTime;
    }

    String timezoneId = restaurant.restaurantSettings.timezoneValue;
    String differenceString = TimezoneValues.byId(timezoneId);
    int difference = (double.parse(differenceString) * 60).toInt();
    return dateTime.add(Duration(minutes: difference));
  }

  String? get runnerId => json["runnerId"];

  String get formattedUpdatedAt => DateFormat(_dateTimeFormat).format(updatedAt);

  String get companyId => json["companyId"];

  String get revenueCenterId => json["revenueCenterId"];

  String? get tableId => json["tableId"];



  String get statusId => json["statusId"];

  Iterable<int> get kotNos => _items.map((e) => e.kotNo).toSet();

  OrderStatus get derivedStatus {
    Set<OrderStatus> set = {};
    for (OrderItem item in nonEmptyItems) {
      set.add(item.status);
    }
    if (set.contains(OrderStatus.pending)) {
      return OrderStatus.pending;
    }
    if (set.contains(OrderStatus.approved)) {
      return OrderStatus.approved;
    }
    if (set.contains(OrderStatus.ready)) {
      return OrderStatus.ready;
    }
    if (set.contains(OrderStatus.completed)) {
      return OrderStatus.completed;
    }
    return set.first;
  }

  String get orderTypeId => json["orderTypeId"];

  String get paymentStatusId => json["paymentStatusId"];

  OrderStatus get status => OrderStatus.fromId(statusId);

  PaymentStatus get paymentStatus => PaymentStatus.fromId(paymentStatusId);

  OrderType get type => OrderType.fromId(orderTypeId);


  String get invoiceNumber => json["invoiceNumber"] ?? "N/A";

  DateTime get lastKotCreatedAt {
    final x = List<OrderItem>.from(_items)
      ..sort(
        (a, b) => b.createdAt.compareTo(a.createdAt),
      );

    try {
      final lastKot = x.firstWhere(
        (element) => element.status == OrderStatus.approved,
      );
      return lastKot.createdAt;
    } catch (e) {
      //  ignore: no last approved
    }
    return x.first.createdAt;
  }

  /// Has at least one [nonEmptyItems] in order
  bool get hasValidItems => nonEmptyItems.isNotEmpty;

  String? getInstructionsOfKot(int kotNo) {
    try {
      return items.firstWhere((element) => element.kotNo == kotNo).instructions;
    } catch (_) {
      //  ignore
    }
    return null;
  }

  final OrderItemsList _items = [];

  final AdditionalChargeList additionalCharges = [];

  OrderItemsIterable get items => _items;

  List<int> get pendingKots {
    List<int> kotNos = [];
    for (OrderItem orderItem in nonEmptyItems) {
      if (orderItem.status == OrderStatus.pending && !kotNos.contains(orderItem.kotNo)) {
        kotNos.add(orderItem.kotNo);
      }
    }
    return kotNos;
  }

  OrderItemsIterable get nonEmptyItems => _items.where(
        (element) => element.quantity > 0 && !OrderStatus.invalidStatuses.contains(element.status),
      );

  OrderItemsIterable get pendingItems => _items.where(
        (element) => element.status == OrderStatus.pending,
      );

  bool hasItemsOfStatus(Iterable<OrderStatus> statuses) => nonEmptyItems.any(
        (element) => statuses.contains(element.status),
      );

  bool get hasApprovedItems => nonEmptyItems.any(
        (element) => element.status == OrderStatus.approved,
      );

  bool get hasPendingItems => nonEmptyItems.any(
        (element) => element.status == OrderStatus.pending,
      );

  bool get hasAllItemReady => nonEmptyItems.every(
        (element) => element.status == OrderStatus.ready,
      );

  void setItems(OrderItemsIterable items) => _items
    ..clear()
    ..addAll(items);

  void setAdditionalCharges(AdditionalChargeIterable additionalCharges) => this.additionalCharges
    ..clear()
    ..addAll(additionalCharges);

  bool get isCancelled => derivedStatus == OrderStatus.cancelled || status == OrderStatus.declined;

  bool get isCompleted => status == OrderStatus.completed;

  bool get isPaid => paymentStatus == PaymentStatus.paid;

  bool get isUnpaid => paymentStatus == PaymentStatus.unpaid;

  void modifyStatus(OrderStatus status) {
    for (OrderItem item in _items) {
      item.modifyStatus(status);
    }
    json["statusId"] = status.id;
  }

  @override
  String toString() {
    String x = "Order $no\n"
        "Status: $status\n";
    for (OrderItem item in items) {
      x += "-\t ${item.name} : ${item.quantity} : ${item.status}\n";
      if (item.addons.isNotEmpty) {
        "Add-On: ${item.addons.map((e) => e.item.name).join(", ")}";
      }
    }
    return x;
  }
}
