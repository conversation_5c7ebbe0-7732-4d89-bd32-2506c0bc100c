import 'package:dineazy_guest_kiosk/providers/cart_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../common/constants/colors.dart';
import '../../widgets/cart_details_item.dart';

class CartDetailsScreen extends StatefulWidget {
  const CartDetailsScreen({super.key});

  @override
  State<CartDetailsScreen> createState() => _CartDetailsScreenState();
}

class _CartDetailsScreenState extends State<CartDetailsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;

  // late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 400), vsync: this);

    // _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: Curves.elasticOut));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void checkout() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 25),
            SizedBox(width: 12),
            Text("Processing Payment...", style: TextStyle(fontWeight: FontWeight.w500, fontSize: 28)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final CartProvider cartProvider = Provider.of<CartProvider>(context);
    final cartItems = cartProvider.cartItems;
    final totalPrice = cartProvider.totalPrice;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [AppColors.primary.withAlpha(5), Colors.grey[50]!, Colors.white]),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // App Bar Section
                    Container(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          // GestureDetector(
                          //   onTap: () {
                          //       context.go('/'); // fallback if it's the root
                          //   },
                          //   child: Container(
                          //     decoration: BoxDecoration(
                          //       color: AppColors.white,
                          //       borderRadius: BorderRadius.circular(16),
                          //       boxShadow: [BoxShadow(color: AppColors.black.withAlpha(1), blurRadius: 10, offset: const Offset(0, 4))],
                          //     ),
                          //     child: Material(
                          //       color: AppColors.transparent,
                          //       child: InkWell(
                          //         onTap: () => Navigator.of(context).pop(),
                          //         borderRadius: BorderRadius.circular(16),
                          //         child: Container(
                          //           padding: const EdgeInsets.all(16),
                          //           child: Icon(Icons.arrow_back_ios, color: AppColors.primary, size: 20),
                          //         ),
                          //       ),
                          //     ),
                          //   ),
                          // ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  "Your Cart",
                                  style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: AppColors.black, letterSpacing: -0.5),
                                ),
                                if (cartItems.isNotEmpty)
                                  Text(
                                    "${cartItems.length} item${cartItems.length > 1 ? 's' : ''} in cart",
                                    style: TextStyle(fontSize: 16, color: AppColors.black, fontWeight: FontWeight.w500),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Main Content Area
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [Expanded(child: cartItems.isEmpty ? _buildEmptyCartState() : _buildCartItemsList(cartItems, cartProvider))],
                        ),
                      ),
                    ),

                    if (cartItems.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          boxShadow: [BoxShadow(color: Colors.black.withAlpha(5), blurRadius: 20, offset: const Offset(0, -10))],
                        ),
                        child: SafeArea(top: false, child: Column(children: [_buildTotalSection(totalPrice), const SizedBox(height: 20), _buildCheckoutButton(totalPrice)])),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyCartState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              shape: BoxShape.circle,
              boxShadow: [BoxShadow(color: Colors.black.withAlpha(1), blurRadius: 20, offset: const Offset(0, 8))],
            ),
            child: Icon(Icons.shopping_cart_outlined, size: 80, color: AppColors.black.withAlpha(50)),
          ),
          const SizedBox(height: 32),
          Text(
            "Your cart is empty",
            style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: AppColors.black),
          ),
          const SizedBox(height: 12),
          Text(
            "Add some delicious items to get started",
            style: TextStyle(fontSize: 24, color: AppColors.primary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          GestureDetector(
            onTap: () {
              context.go('/');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(15),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: AppColors.primary.withAlpha(2)),
              ),
              child: Text(
                "Browse Menu",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.primary),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItemsList(Map cartItems, CartProvider cartProvider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(5), blurRadius: 15, offset: const Offset(0, 5))],
      ),
      child: ListView.separated(
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.all(20),
        itemCount: cartItems.length,
        separatorBuilder: (_, __) => Container(
          height: 1,
          margin: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(gradient: LinearGradient(colors: [Colors.transparent, AppColors.buttonDisabled.withAlpha(3), Colors.transparent])),
        ),
        itemBuilder: (context, index) {
          final itemName = cartItems.keys.elementAt(index);
          final item = cartItems[itemName]!;
          return AnimatedContainer(
            duration: Duration(milliseconds: 300 + (index * 100)),
            curve: Curves.easeOutBack,
            child: CartDetailsItem(itemName: itemName, quantity: item["quantity"], price: item["price"], imagePath: item["image"], onRemove: () => cartProvider.removeCartItem(itemName)),
          );
        },
      ),
    );
  }

  Widget _buildTotalSection(double totalPrice) {
    return Container(
      padding: const EdgeInsets.only(left: 200, right: 30),
      decoration: BoxDecoration(
        gradient: LinearGradient(begin: Alignment.topLeft, end: Alignment.bottomRight, colors: [AppColors.primary.withAlpha(1), AppColors.primary.withAlpha(5)]),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.primary.withAlpha(2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Total Amount",
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.w500, color: AppColors.black),
              ),
              const SizedBox(height: 4),
              const Text("Including all taxes", style: TextStyle(fontSize: 20, color: Colors.grey)),
            ],
          ),
          Material(
            borderRadius: BorderRadius.circular(16),
            color: AppColors.primary,
            child: InkWell(
              onTap: checkout,
              borderRadius: BorderRadius.circular(30),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.payment, color: Colors.white, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      "Pay \$${totalPrice.toStringAsFixed(2)}",
                      style: const TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(color: Colors.white.withAlpha(2), borderRadius: BorderRadius.circular(8)),
                      child: const Icon(Icons.arrow_forward, color: Colors.white, size: 16),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Container(
          //   padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          //   decoration: BoxDecoration(
          //     color: AppColors.primary,
          //     borderRadius: BorderRadius.circular(12),
          //     boxShadow: [BoxShadow(color: AppColors.primary.withAlpha(3), blurRadius: 8, offset: const Offset(0, 4))],
          //   ),
          //   child: Text(
          //     "\$${totalPrice.toStringAsFixed(2)}",
          //     style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildCheckoutButton(double totalPrice) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [Padding(padding: const EdgeInsets.only(right: 30))],
    );
  }
}
