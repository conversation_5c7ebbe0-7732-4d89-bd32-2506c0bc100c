import 'package:flutter/material.dart';

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _fadeController;

  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _pulseController = AnimationController(duration: const Duration(milliseconds: 2000), vsync: this);

    _rotationController = AnimationController(duration: const Duration(milliseconds: 3000), vsync: this);

    _scaleController = AnimationController(duration: const Duration(milliseconds: 1500), vsync: this);

    _fadeController = AnimationController(duration: const Duration(milliseconds: 1000), vsync: this);

    // Initialize animations
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut));

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2,
    ).animate(CurvedAnimation(parent: _rotationController, curve: Curves.linear));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut));

    _fadeAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut));

    // Start animations
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    _scaleController.repeat(reverse: true);
    _fadeController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667eea), Color(0xFF764ba2), Color(0xFFf093fb), Color(0xFFf5576c)],
            stops: [0.0, 0.33, 0.66, 1.0],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Animated logo/icon area
                AnimatedBuilder(
                  animation: Listenable.merge([_pulseAnimation, _rotationAnimation, _scaleAnimation]),
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value * _scaleAnimation.value,
                      child: Transform.rotate(
                        angle: _rotationAnimation.value * 3.14159,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Color(0xFFffecd2), Color(0xFFfcb69f)],
                            ),
                            boxShadow: [
                              BoxShadow(color: Colors.white.withOpacity(0.3), blurRadius: 20, spreadRadius: 5),
                            ],
                          ),
                          child: const Icon(Icons.rocket_launch, size: 60, color: Colors.white),
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 60),

                // App name with fade animation
                AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value,
                      child: const Text(
                        'YourApp',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 2.0,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 10),

                // Tagline
                AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value * 0.8,
                      child: const Text(
                        'Loading amazing experience...',
                        style: TextStyle(fontSize: 16, color: Colors.white70, fontWeight: FontWeight.w300),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 80),

                // Custom loading indicator
                SizedBox(width: 200, child: _buildCustomLoadingIndicator()),

                const SizedBox(height: 40),

                // Loading text with dots animation
                _buildLoadingText(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCustomLoadingIndicator() {
    return AnimatedBuilder(
      animation: _rotationController,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // Background circle
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white.withOpacity(0.2), width: 3),
              ),
            ),
            // Animated arc
            Transform.rotate(
              angle: _rotationController.value * 2 * 3.14159,
              child: SizedBox(
                width: 60,
                height: 60,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white.withOpacity(0.8)),
                ),
              ),
            ),
            // Center dot
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Container(
                  width: 8 * _pulseAnimation.value,
                  height: 8 * _pulseAnimation.value,
                  decoration: const BoxDecoration(shape: BoxShape.circle, color: Colors.white),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildLoadingText() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        int dotCount = (_pulseController.value * 3).round();
        String dots = '.' * (dotCount + 1);

        return Text(
          'Loading$dots',
          style: TextStyle(fontSize: 18, color: Colors.white.withOpacity(0.9), fontWeight: FontWeight.w400),
        );
      },
    );
  }
}
