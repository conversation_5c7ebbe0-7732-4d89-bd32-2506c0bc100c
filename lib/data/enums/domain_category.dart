enum CompanyDomainCategory {
  timeFormat("Time Format","6373cd96789c5e61fb93ac90"),
  dateFormat("Date Format","6373cda1789c5e61fb93ac91"),
  currency("Currency","6373cdad789c5e61fb93ac92"),
  timezone("Timezone","6373cdb6789c5e61fb93ac93"),
  feedbackInputs("Feedback Inputs","63b857bcdc2c227613583972");

  final String id;
  final String name;
  const CompanyDomainCategory(this.name,this.id);
}
enum RvcDomainCategory {
  additionalCharges("Additional Charges","63a5288af140c567b8c05f95"),
  autoCharges("Auto Charges","64c73bd2d3cd2802b4d1d724"),
  tableTypeCategory("Table Type Catagory","6375e6d024b4c4b6f982d4cb");

  final String name;
  final String id;
  const RvcDomainCategory(this.name, this.id);
}